export type RiskScoreReferenceType =
  | 'assessment'
  | 'attachment'
  | 'careplan'
  | 'note'
  | 'hospitalization';

export interface RiskScoreReference {
  type: RiskScoreReferenceType;
  ref_id: string;
  title: string;
  reason: string;
}

export interface ReferenceItem {
  id: string;
  title: string;
  createdAt?: string;
}

export interface RiskScoreUser {
  id: string;
  firstName?: string;
  lastName?: string;
  email?: string;
}

export interface RiskScoreCreate {
  score: number;
  level: 'Low' | 'Medium' | 'High';
  source: 'Manual' | 'AssessmentEngine' | 'Other';
  notes?: string;
  references?: RiskScoreReference[];
}

export interface RiskScore extends RiskScoreCreate {
  id: string;
  createdAt: string;
  updatedAt: string;
  memberId: string;
}

export interface RiskScoreHistory {
  id: string;
  score: number;
  level: 'Low' | 'Medium' | 'High';
  source: 'Manual' | 'AssessmentEngine' | 'Other';
  createdAt: string;
  notes?: string;
}

export interface RiskScoreDetail extends RiskScoreCreate {
  id: string;
  notes: string;
  createdAt: string;
  updatedAt: string;
  createdByUser?: RiskScoreUser;
  references?: RiskScoreReference[];
}
