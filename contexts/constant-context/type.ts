import { Carrier } from 'types/service-types';

export interface Fullhrp {
    title: string;
    color: string;
    question_score: number;
    overall_score_range: number[];
}

export interface Langauge {
    color: string;
    key: string;
    title: string;
}

export interface AttachmentType {
    key: string;
    title: string;
}

export interface Scores {
    full_hrp: Fullhrp[];
}
export interface ConstantContext {
    carriers: any[];
    taskCompletionReasons: AttachmentType[];
    homeless: AttachmentType[];
    relationship: AttachmentType[];
    networkStatus: AttachmentType[];
    contactTaskTypes: AttachmentType[];
    hospitalization: AttachmentType[];
    taskCompletionTypes: AttachmentType[];
    attachmentTypes: AttachmentType[];
    militaryStatus: AttachmentType[];
    taskTypes: AttachmentType[];
    pronouns: string[];
    reasons: AttachmentType[];
    staffStatus: AttachmentType[];
    nationalities: string[];
    roleTypes: string[];
    roles: AttachmentType[];
    carepackageTypes: AttachmentType[];
    pregnancyStatus: AttachmentType[];
    surveyTypes: AttachmentType[];
    genderIndentity: string[];
    carePackageSections: AttachmentType[];
    sexualIdentities: string[];
    memberTypes: AttachmentType[];
    ethnicities: AttachmentType[];
    langauges: Langauge[];
    scores: Scores;
    networkServices: AttachmentType[];
    consentLangs: string[];
    uploadTypes: string[];
    notesSubcategory: Langauge[];
    noteTags: Langauge[];
    states: AttachmentType[];
    timelineStatus: AttachmentType[];
    planTypes: AttachmentType[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    memberTags: any[];
    goalTypes: AttachmentType[];
    interventionTypes: AttachmentType[];
    followUpTypes: AttachmentType[];
    followUpOutcomes: AttachmentType[];
    problemTypes: AttachmentType[];
    problemOutcomes: AttachmentType[];
    reviewFrequencies: AttachmentType[];
    noteTypes: AttachmentType[];
    reviewTypes: AttachmentType[];
    programStatuses: AttachmentType[];
    programTypes: AttachmentType[];
    associatedPersonRole: AttachmentType[];
    assessmentDueOptions: AttachmentType[];
    riskScoreLevels: AttachmentType[];
    riskScoreSources: AttachmentType[];
}
