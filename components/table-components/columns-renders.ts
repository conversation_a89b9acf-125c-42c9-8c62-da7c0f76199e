import { TableHeader } from 'types/navigators-types';

export const columns = [
  { key: 'firstName', value: 'Full Name' },
  { key: 'title', value: 'Title' },
  { key: 'name', value: 'Name' },
  { key: 'dob', value: 'Date of Birth' },
  { key: 'status', value: 'Status' },
  { key: 'household', value: 'Household' },
  { key: 'gender', value: 'Gender' },
  { key: 'genderIdentity', value: 'Gender Identity' },
  { key: 'pronouns', value: 'Pronouns' },
  { key: 'sexualIdentity', value: 'Sexual Identity' },
  { key: 'ethnicity', value: 'Ethnicity' },
  { key: 'lang', value: 'Language' },
  { key: 'military', value: 'Military Status' },
  { key: 'email', value: 'Email' },
  { key: 'quickNotes', value: 'Quick Notes' },
  { key: 'notes', value: 'Notes' },
  { key: 'lastContact', value: 'Last Contact' },
  { key: 'referredBy', value: 'Referred By' },
  { key: 'teams', value: 'Teams' },
  { key: 'team', value: 'Team' },
  { key: 'score', value: 'Score' },
  { key: 'householdScore', value: 'Household Score' },
  { key: 'type', value: 'Type' },
  { key: 'services', value: 'Services' },
  { key: 'desc', value: 'Description' },
  { key: 'address', value: 'Address' },
  { key: 'city', value: 'City' },
  { key: 'state', value: 'State' },
  { key: 'zip', value: 'Zip' },
  { key: 'lastVisit', value: 'Last House Visit' },
  { key: 'lastAt', value: 'Last Assessment' },
  { key: 'updatedAt', value: 'Last Updated' },
  { key: 'navigators', value: 'Navigators' },
  { key: 'households', value: 'Households' },
  { key: 'members', value: 'Members' },
  { key: 'actions', value: '' },
  { key: 'role', value: 'Role on Team' },
  { key: 'carrier', value: 'Carrier' },
  { key: 'score', value: 'Risk Score' },
  { key: 'level', value: 'Level' },
  { key: 'source', value: 'Source' },
  { key: 'createdAt', value: 'Created' },
  { key: 'planType', value: 'Plan Type' },
  { key: 'policyNumber', value: 'Member No.' },
  { key: 'startDate', value: 'Coverage Start' },
  { key: 'endDate', value: 'Coverage End' },
  { key: 'items', value: 'Services' },
  { key: 'creator', value: 'Navigator' },
  { key: 'createdAt', value: 'Created' },
  { key: 'distance', value: 'Distance' },
  { key: 'memberBooking', value: 'Member Booking' },
  { key: 'statusNote', value: 'Status Note' },
  { key: 'lastReviewed', value: 'Last Follow-up' },
  { key: 'goals', value: 'Goals' },
  { key: 'interventions', value: 'Interventions' },
  { key: 'goalType', value: 'Type' },
  { key: 'targetDate', value: 'Target Date' },
  { key: 'goalObjective', value: 'Goal Objective' },
  { key: 'responsibleParty', value: 'Responsible Party' },
  { key: 'dueDate', value: 'Due Date' },
  { key: 'interventionObjective', value: 'Intervention Objective' },
  { key: 'author', value: 'Author' },
  { key: 'date', value: 'Date' },
  { key: 'note', value: 'Note' },
  { key: 'by', value: 'By' },
  { key: 'status', value: 'Status' },
  { key: 'primaryNavigator', value: 'Primary Navigator' },
  { key: 'description', value: 'Description' },
  { key: 'dateIdentified', value: 'Date Identified' },
  { key: 'clinicalNote', value: 'Clinical Note' },
  { key: 'services', value: 'Services' },
  { key: 'providerName', value: 'Provider' },
  { key: 'networkName', value: 'Title' },
  { key: 'code', value: 'Code' },
  { key: 'generic', value: 'Generic' },
  { key: 'carePlanStartDate', value: 'Start Date' },
  { key: 'startAndEndDate', value: 'Coverage Start & End' },
  { key: 'relationship', value: 'Relationship' },
  { key: 'phone', value: 'Phone' },
  { key: 'phones', value: 'Phone' },
  { key: 'primaryNavigator', value: 'Primary Navigator' },
  { key: 'enrollmentDate', value: 'Enrollment Date' },
  { key: 'reviewFrequency', value: 'Review Frequency' },
  { key: 'reviewsCompleted', value: 'Reviews Completed' },
  { key: 'nextReview', value: 'Next Review' },
];
// eslint-disable-next-line import/prefer-default-export
export const columnsRender = (variant:string, config: any) => {
  if (variant === 'main-household') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'address', 'members', 'teams', 'householdScore', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'report-full-data') {
    // Select columns based on specified indexes
    const selectedKeys = ['name', 'value', 'percentage'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'main-member-table') {
    const defaultKeys = ['firstName', 'dob', 'status', 'household', 'teams', 'score', 'actions'];

    // // Filter the columns array based on these keys
    const selectedKeys = config.length === 0 ? defaultKeys : config[variant] ? [...JSON?.parse(config[variant]), 'actions'] : defaultKeys;

    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key:any) => filteredColumns.find((column) => column.key === key)).filter((column:any) => column);
    return orderedColumns;
  }
  if (variant === 'addsection-member-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['firstName', 'dob', 'type', 'genderIdentity', 'household', 'ethnicity', 'language', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }

  if (variant === 'addsection-team-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }

  if (variant === 'main-team-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'navigators', 'households', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'navigator-team-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'role', 'navigators', 'households', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'addsection-navigator-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['name', 'teams', 'email', 'actions'];

    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'addsection-service-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'desc', 'type', 'actions'];

    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'main-navigator-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['name', 'teams', 'email', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'social-plan-network-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'address', 'distance', 'services', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'social-plan-network-item-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'status', 'statusNote', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'social-plan-view-network-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['name', 'types', 'address', 'services', 'desc', 'updatedAt', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'main-network-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'types', 'address', 'services', 'updatedAt', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'select-network-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'address', 'distance', 'services', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'chat-participant-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['name', 'role', 'teams', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'member-tags-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'updatedAt', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'household-tags-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'updatedAt', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }

  if (variant === 'select-insurance-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['name', 'type', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'addsection-insurance-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['name', 'type', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'add-navigator-team-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'navigators', 'households']; // members to be added later
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'navigator-team-table-add-update') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'navigators', 'households', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'member-insurances-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['carrier', 'planType', 'policyNumber', 'startDate', 'endDate', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'social-plan-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'createdAt', 'creator', 'items', 'status', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'address-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['address'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'consents-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['name', 'createdAt'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'care-plans-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'carePlanStartDate', 'lastReviewed', 'goals', 'interventions', 'status', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'goals-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'goalType', 'targetDate', 'goalObjective', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'interventions-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'responsibleParty', 'dueDate', 'note', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'reviews-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'author', 'date', 'note', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'follow-ups-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['type', 'by', 'date', 'note', 'status', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'notes-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'author', 'date', 'note', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'team-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'navigators', 'primaryNavigator', 'households', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'problems-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'code', 'dateIdentified', 'clinicalNote', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'select-network-appointments-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'type', 'address', 'services'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'network-appointments-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['networkName', 'providerName', 'date', 'note', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'select-diagnosis-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'description', 'code'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'select-medication-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['name', 'generic', 'code'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'profile-diagnoses-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'code', 'status', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'profile-medications-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'description', 'code', 'status', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'profile-problems-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'description', 'type', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'profile-insurances-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['carrier', 'planType', 'policyNumber', 'startAndEndDate', 'status', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'profile-contacts-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['name', 'type', 'relationship', 'phone', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'profile-teams-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'navigators', 'primaryNavigator', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'member-programs-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'enrollmentDate', 'endDate', 'reviewFrequency', 'reviewsCompleted', 'nextReview', 'status', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  if (variant === 'member-risk-scores-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['score', 'level', 'source', 'notes', 'createdAt', 'actions'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }

  if (variant === 'main-connected-network-table') {
    // Select columns based on specified indexes
    const selectedKeys = ['title', 'name', 'address', 'phones', 'desc'];
    // Filter the columns array based on these keys
    // eslint-disable-next-line max-len
    const filteredColumns = columns.filter((column:any) => selectedKeys.includes(column.key)) as TableHeader[];
    // eslint-disable-next-line max-len
    const orderedColumns = selectedKeys.map((key) => filteredColumns.find((column) => column.key === key)).filter((column) => column);
    return orderedColumns;
  }
  return columns as TableHeader[];
};
