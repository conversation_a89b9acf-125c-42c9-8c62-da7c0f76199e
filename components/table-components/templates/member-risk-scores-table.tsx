/* eslint-disable max-len */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import { capitalizeWords } from 'utils/helper';
import API from 'api/src/lib/api';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import SafeDate from 'components/date-time/SafeDate';
import { useModal } from 'contexts/modal-context/modal-context';
import { useRouter } from 'next/router';
import { TableRenderProps } from '../render';

const pStyle = 'text-[15px] p-0 m-0 py-3 text-[#262D2D] font-[500]';

function MemberRiskScoresTable(props: TableRenderProps<any>) {
  const {
    data: {
      score,
      level,
      source,
      createdAt,
      id,
    },
    children,
    value,
    sKey,
    handleDelete,
  } = props;
  const { refresh } = useRefreshSSR();
  const { showModal } = useModal();
  const router = useRouter();
  const memberId = props.metadata?.memberId;
  const riskScoreId = props.data?.id

  console.log('Risk score ID:', riskScoreId);
  console.log('props', props);

  const levelColors = {
    Low: '#28a745',
    Medium: '#ffc107',
    High: '#dc3545',
  };

  const handleDeleteRiskScore = async (id: string) => {
    showModal(
      'Warning',
      'Are you sure you want to remove this risk score?',
      async () => {
        // TODO: Implement delete API when available
        // const response = await API.MEMBERS.deleteMemberRiskScore(id);
        // if (response) {
        //   refresh();
        // }
        console.log('Delete risk score:', id);
        refresh();
      },
    );
  };

  switch (sKey) {
    case 'riskScore':
      return (
        <div className="flex items-center">
          {children}
          <div className="flex items-center gap-2">
            <span className={pStyle}>{score || '-'}</span>
            <span className="text-gray-400">|</span>
            <span
              className={pStyle}
              style={{
                color: levelColors[level as keyof typeof levelColors] || '#6E6E6E',
                fontWeight: '600'
              }}
            >
              {capitalizeWords(level, true) || '-'}
            </span>
          </div>
        </div>
      );
    case 'source':
      return (
        <p className={pStyle}>
          {capitalizeWords(source, true) || '-'}
        </p>
      );
    case 'createdAt':
      return (
        <p className={pStyle}>
          <SafeDate date={createdAt} />
        </p>
      );
    case 'actions': {
      // More robust ID extraction with better fallback logic
      // Try multiple possible ID field names to handle different data structures
      const riskScoreId = id
        || props.data?.id
        || props.data?.riskScoreId
        || props.data?.acuityId
        || props.data?.scoreId
        || props.data?._id; // MongoDB style ID as additional fallback
        console.log('Risk score ID:', riskScoreId);
        console.log('props', props);

      return (
        <div className="flex justify-center items-center w-[50px]">
          <ActionContainer
            viewLabel="View Score"
            onView={() => {
              if (!memberId || !riskScoreId) {
                console.warn('Cannot navigate to risk score details: missing memberId or riskScoreId', {
                  memberId,
                  riskScoreId,
                  data: props.data
                });
                return;
              }
              router.push(`/members/risk-scores/${memberId}/${riskScoreId}`);
            }}
            deleteLabel="Delete Risk Score"
            onDelete={() => {
              if (!riskScoreId) {
                console.warn('Cannot delete risk score: missing riskScoreId', {
                  riskScoreId,
                  data: props.data
                });
                return;
              }
              handleDeleteRiskScore(riskScoreId);
            }}
            // editLabel="Edit Risk Score"
            // onEdit={() => router.push(`/members/risk-scores/${id}/edit`)}
          />
        </div>
      );
    }
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default MemberRiskScoresTable;
