/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable no-unused-vars */
import {
  Modal,
  TextField,
  Button,
} from '@mui/material';
import React, {
  useState, useRef, useContext,
} from 'react';
import {
  calculateAge, capitalizeWords, getKeyValue,
} from 'utils/helper';
import Close from '@mui/icons-material/Close';
import { Add, Delete } from '@mui/icons-material';
import SelectBox from 'components/select-box/select-box';
import { RiskScoreCreate, RiskScoreReference, ReferenceItem } from 'types/risk-scores-types';
import API from 'api/src/lib/api';
import { ConstantsContext } from 'contexts/constant-context/constant-context';

interface AddRiskScoreModalProps {
  open: boolean;
  setOpen: (value: boolean) => void;
  handleAdd: (data: RiskScoreCreate) => void;
  memberId: string;
}

function AddRiskScoreModal({
  open,
  setOpen,
  handleAdd,
  memberId,
}: AddRiskScoreModalProps) {
  const backdropRef = useRef<HTMLDivElement | null>(null);
  const mouseDownOnBackdrop = useRef(false);
  const constants = useContext(ConstantsContext);

  const [formValues, setFormValues] = useState<Partial<RiskScoreCreate>>({
    score: undefined,
    level: undefined,
    source: 'Manual',
    notes: '',
    references: [],
  });
  const [missingFields, setMissingFields] = useState<string[]>([]);

  // References state
  const [references, setReferences] = useState<RiskScoreReference[]>([]);
  const [showAddReference, setShowAddReference] = useState(false);
  const [newReference, setNewReference] = useState<Partial<RiskScoreReference>>({
    type: undefined,
    ref_id: '',
    title: '',
    reason: '',
  });
  const [availableItems, setAvailableItems] = useState<ReferenceItem[]>([]);
  const [loadingItems, setLoadingItems] = useState(false);
  const hasReferences = references.length > 0;
  const isReferenceFormComplete = Boolean(newReference.type && newReference.ref_id && newReference.reason);

  const resetReferenceForm = () => {
    setShowAddReference(false);
    setNewReference({
      type: undefined,
      ref_id: '',
      title: '',
      reason: '',
    });
    setAvailableItems([]);
  };



  const referenceTypeOptions = [
    { label: 'Assessment', value: 'assessment' },
    { label: 'Care Plan', value: 'careplan' },
    { label: 'Attachment', value: 'attachment' },
    { label: 'Note', value: 'note' },
  ];

  // Fetch reference items based on type
  const fetchReferenceItems = async (type: string) => {
    if (!type || !memberId) return;

    setLoadingItems(true);
    try {
      let items: ReferenceItem[] = [];

      switch (type) {
        case 'assessment':
          const assessments = await API.MEMBERS.fetchMemberEncounters(memberId, 'all', 0, 100);
          items = (assessments as any)?.items?.map((item: any) => ({
            id: String(item.id),
            title: item.name || 'Assessment',
            createdAt: item.createdAt,
          })) || [];
          break;

        case 'careplan':
          const carePlans = await API.CAREPLANS.fetchMemberCarePlans(memberId);
          items = (carePlans as any)?.map((item: any) => ({
            id: String(item.id),
            title: item.title || 'Care Plan',
            createdAt: item.createdAt,
          })) || [];
          break;

        case 'attachment':
          const attachments = await API.MEMBERS.fetchMemberAttachments(memberId, 'all');
          items = (attachments as any)?.items?.map((item: any) => ({
            id: String(item.id),
            title: item.name || item.title || 'Attachment',
            createdAt: item.createdAt,
          })) || [];
          break;

        case 'note':
          const notes = await API.MEMBERS.fetchMemberNotes(memberId);
          items = (notes as any)?.items?.map((item: any) => ({
            id: String(item.id),
            title: item.title || 'Note',
            createdAt: item.createdAt,
          })) || [];
          break;

        default:
          items = [];
      }

      setAvailableItems(items);
    } catch (error) {
      console.error('Error fetching reference items:', error);
      setAvailableItems([]);
    } finally {
      setLoadingItems(false);
    }
  };

  const validateFields = () => {
    const missing: string[] = [];
    if (!formValues.score || formValues.score < 1 || formValues.score > 10) {
      missing.push('score');
    }
    if (!formValues.level) {
      missing.push('level');
    }
    if (!formValues.source) {
      missing.push('source');
    }
    setMissingFields(missing);
    return missing.length === 0;
  };

  // Reference management functions
  const handleReferenceTypeChange = (type: string) => {
    setNewReference((prev) => ({
      ...prev,
      type: type as RiskScoreReference['type'] | undefined,
      ref_id: '',
      title: '',
    }));
    fetchReferenceItems(type);
  };

  const handleReferenceItemSelect = (itemId: string) => {
    if (!itemId) {
      setNewReference((prev) => ({
        ...prev,
        ref_id: '',
        title: '',
      }));
      return;
    }

    const selectedItem = availableItems.find((item) => (
      (item.id || '')?.toString().toLowerCase() === itemId.toLowerCase()
    ));
    if (selectedItem) {
      setNewReference((prev) => ({
        ...prev,
        ref_id: selectedItem.id,
        title: selectedItem.title,
      }));
    }
  };

  const addReference = () => {
    if (!newReference.type || !newReference.ref_id || !newReference.title || !newReference.reason) {
      return;
    }

    const reference: RiskScoreReference = {
      type: newReference.type!,
      ref_id: newReference.ref_id,
      title: newReference.title,
      reason: newReference.reason,
    };

    setReferences((prev) => [...prev, reference]);
    setNewReference({
      type: undefined,
      ref_id: '',
      title: '',
      reason: '',
    });
    setAvailableItems([]);
    // Keep the form open for adding more references
    // setShowAddReference(false); // Removed this line
  };

  const removeReference = (index: number) => {
    setReferences(references.filter((_, i) => i !== index));
  };

  const handleSubmit = () => {
    if (!validateFields()) return;

    const riskScoreData: RiskScoreCreate = {
      score: formValues.score!,
      level: formValues.level!,
      source: formValues.source!,
      notes: formValues.notes || '',
      references: references,
    };

    // Debug logging to see what's being sent
    console.log('🔍 Risk Score Data being sent:', riskScoreData);
    console.log('📎 References being sent:', references);
    console.log('📊 References count:', references.length);

    handleAdd(riskScoreData);
    setFormValues({
      score: undefined,
      level: undefined,
      source: 'Manual',
      notes: '',
      references: [],
    });
    setReferences([]);
    setMissingFields([]);
    setOpen(false);
  };

  const handleInputChange = (field: keyof RiskScoreCreate, value: any) => {
    setFormValues(prev => ({
      ...prev,
      [field]: value,
    }));
    // Remove field from missing fields if it's now filled
    if (missingFields.includes(field)) {
      setMissingFields(prev => prev.filter(f => f !== field));
    }
  };

  const scrollbarStyles = `
    .custom-scrollbar::-webkit-scrollbar {
      width: 8px;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  `;

  return (
    <Modal
      open={open}
      onClose={() => setOpen(false)}
      className="flex items-center justify-center"
    >
      <div
        ref={backdropRef}
        className="flex items-center justify-center w-full h-full bg-black bg-opacity-50"
        onMouseDown={(e) => {
          mouseDownOnBackdrop.current = e.target === backdropRef.current;
        }}
        onMouseUp={(e) => {
          if (mouseDownOnBackdrop.current && e.target === backdropRef.current) {
            setOpen(false);
          }
          mouseDownOnBackdrop.current = false;
        }}
      >
        <div
          className="flex flex-col h-[96vh] w-[95vw] bg-white rounded-lg overflow-hidden"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              handleSubmit();
            }
          }}
          tabIndex={0}
        >
          <style>{scrollbarStyles}</style>
          {/* Header */}
          <div
            style={{ borderBottom: '1px solid #E0E0E0' }}
            className="h-[64px] shrink-0 items-center relative justify-center grid grid-flow-col"
          >
            <div className="grid grid-flow-row p-4 items-center text-center">
              <p className="m-0 font-[500] text-[18px]">
                Add Risk Score
              </p>
            </div>
            <Close
              className="absolute right-5 cursor-pointer"
              onClick={() => setOpen(false)}
            />
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-8 custom-scrollbar bg-gray-50">
            <div className="w-full space-y-6">
              {/* Basic Information Section */}
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-6">
                  Basic Information
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Score Input */}
                  <div>
                    <TextField
                      label="Risk Score (1-10)"
                      type="number"
                      value={formValues.score || ''}
                      onChange={(e) => handleInputChange('score', parseInt(e.target.value))}
                      fullWidth
                      variant="filled"
                      inputProps={{ min: 1, max: 10 }}
                      error={missingFields.includes('score')}
                      helperText={missingFields.includes('score') ? 'Score is required (1-10)' : ''}
                    />
                  </div>                  

                  {/* Risk Level Selection */}
                  <div>
                    <SelectBox
                      label="Risk Level"
                      keyVal="riskLevel"
                      defaultValue={formValues.level || ''}
                      onChange={(e) => handleInputChange('level', e.target.value)}
                      items={constants?.riskScoreLevels?.map(opt => ({ key: opt.key, title: opt.title })) || []}
                    />
                    {missingFields.includes('level') && (
                      <p className="text-red-500 text-sm mt-1">Risk level is required</p>
                    )}
                  </div>

                  {/* Source Selection */}
                  <div>
                    <SelectBox
                      label="Source"
                      keyVal="source"
                      defaultValue={formValues.source || ''}
                      onChange={(e) => handleInputChange('source', e.target.value)}
                      items={constants?.riskScoreSources?.map(opt => ({ key: opt.key, title: opt.title })) || []}
                    />
                    {missingFields.includes('source') && (
                      <p className="text-red-500 text-sm mt-1">Source is required</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Notes Section */}
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-6">
                  Additional Notes
                </h3>
                <TextField
                  label="Additional information"
                  placeholder="Add any additional context or observations..."
                  multiline
                  rows={4}
                  value={formValues.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  fullWidth
                  variant="filled"
                />
              </div>

              {/* References Section */}
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between md:gap-6 mb-6">
                  <div className="space-y-2">
                    <div className="flex items-center gap-3">
                      <h3 className="text-lg font-medium text-gray-900 m-0">Supporting References</h3>
                      {hasReferences && (
                        <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                          {references.length} reference{references.length !== 1 ? 's' : ''} added
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-500 m-0">
                      Link the assessments, notes, care plans, or attachments that justify this risk score.
                    </p>
                  </div>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<Add />}
                    disabled={showAddReference}
                    onClick={() => setShowAddReference(true)}
                    className="h-[36px] text-blue-600 border-blue-300 hover:bg-blue-50 disabled:text-gray-400 disabled:border-gray-200"
                  >
                    {hasReferences ? 'Add Another Reference' : 'Add Reference'}
                  </Button>
                </div>

                <div className={`grid gap-6 ${showAddReference ? 'md:grid-cols-5' : ''}`}>
                  <div className={`${showAddReference ? 'md:col-span-3' : ''} space-y-3`}>
                    {hasReferences ? (
                      references.map((ref, index) => (
                        <div
                          key={`${ref.ref_id}-${index}`}
                          className="border border-gray-200 rounded-lg p-4 bg-white shadow-sm"
                        >
                          <div className="flex items-start justify-between gap-4">
                            <div className="flex-1 space-y-2">
                              <div className="flex items-center gap-3">
                                <span className="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-full font-medium">
                                  {ref.type.charAt(0).toUpperCase() + ref.type.slice(1)}
                                </span>
                                <span className="font-medium text-gray-900">{ref.title}</span>
                              </div>
                              <p className="text-sm text-gray-600 m-0 leading-relaxed">{ref.reason}</p>
                            </div>
                            <button
                              onClick={() => removeReference(index)}
                              className="text-gray-400 hover:text-red-500 p-1"
                              aria-label="Remove reference"
                            >
                              <Delete fontSize="small" />
                            </button>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="rounded-lg border border-dashed border-gray-300 bg-gray-50 p-8 text-center">
                        <p className="text-sm font-medium text-gray-700 mb-2">No supporting references yet</p>
                        <p className="text-sm text-gray-500 m-0">
                          Use “Add Reference” to connect relevant documentation and give reviewers helpful context.
                        </p>
                      </div>
                    )}

                    {showAddReference && hasReferences && (
                      <div className="flex justify-end">
                        <Button
                          variant="text"
                          size="small"
                          onClick={resetReferenceForm}
                          className="text-blue-600 hover:bg-blue-50"
                        >
                          Done Adding References
                        </Button>
                      </div>
                    )}
                  </div>

                  {showAddReference && (
                    <div className="md:col-span-2 space-y-4 rounded-lg border border-gray-200 bg-gray-50 p-6 md:sticky md:top-20">
                      <div className="flex items-start justify-between gap-4">
                        <div>
                          <h4 className="text-base font-medium text-gray-900 m-0">Add New Reference</h4>
                          <p className="text-sm text-gray-500 mt-1 mb-0">Choose the source that supports this score.</p>
                        </div>
                        <button
                          onClick={resetReferenceForm}
                          className="text-gray-400 hover:text-gray-600"
                          aria-label="Close reference form"
                        >
                          <Close fontSize="small" />
                        </button>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <p className="text-sm font-medium mb-2">Reference Type</p>
                          <SelectBox
                            label="Reference Type"
                            defaultValue={newReference.type || ''}
                            keyVal="referenceType"
                            items={referenceTypeOptions.map(opt => ({ key: opt.value, title: opt.label }))}
                            onChange={(e) => handleReferenceTypeChange(e.target.value)}
                          />
                        </div>

                        {newReference.type && (
                          <div className="space-y-2">
                            <p className="text-sm font-medium m-0">
                              Select {newReference.type.charAt(0).toUpperCase() + newReference.type.slice(1)}
                            </p>
                            {loadingItems ? (
                              <p className="text-sm text-gray-500 m-0">Loading...</p>
                            ) : availableItems.length > 0 ? (
                              <SelectBox
                                label={`Select ${newReference.type.charAt(0).toUpperCase() + newReference.type.slice(1)}`}
                                defaultValue={newReference.ref_id ? newReference.ref_id.toLowerCase() : ''}
                                keyVal="referenceItem"
                                items={availableItems.map(item => ({ key: item.id, title: item.title }))}
                                onChange={(e) => handleReferenceItemSelect(e.target.value)}
                              />
                            ) : (
                              <p className="text-sm text-gray-500 m-0">No {newReference.type}s found for this member</p>
                            )}
                          </div>
                        )}

                        {newReference.ref_id && (
                          <TextField
                            label="Reason for Reference"
                            placeholder="Explain how this supports the risk score..."
                            multiline
                            rows={3}
                            value={newReference.reason}
                            onChange={(e) => setNewReference({ ...newReference, reason: e.target.value })}
                            fullWidth
                            size="small"
                          />
                        )}
                      </div>

                      <div className="flex justify-end gap-2 pt-4 border-t border-gray-200">
                        <Button
                          variant="outlined"
                          size="small"
                          onClick={resetReferenceForm}
                          className="h-[32px] text-gray-600 border-gray-300"
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="contained"
                          size="small"
                          onClick={addReference}
                          disabled={!isReferenceFormComplete}
                          className="h-[32px] bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 disabled:text-white disabled:cursor-not-allowed"
                        >
                          Add Reference
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div
            style={{ borderTop: '1px solid #E0E0E0' }}
            className="h-[80px] shrink-0 flex items-center justify-end px-8 gap-3 bg-white"
          >
            <Button
              variant="outlined"
              onClick={() => setOpen(false)}
              className="h-[40px] px-6 text-gray-600 border-gray-300 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={handleSubmit}
              className="h-[40px] px-6 bg-orange-500 hover:bg-orange-600 text-white"
              style={{ backgroundColor: '#FF8C00', color: 'white' }}
            >
              Save Risk Score
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}

export default AddRiskScoreModal;
