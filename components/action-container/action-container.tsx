/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import { Menu, MenuItem } from '@mui/material';
import React from 'react';

interface ActionContainerProps {
  viewLabel?: string;
  deleteLabel?: string;
  makeHead?: boolean;
  editLabel?: string;
  onAction?: () => void;
  onView?: () => void;
  onDelete?: () => void;
  onEdit?: () => void;
}
function ActionContainer({
  viewLabel,
  deleteLabel,
  editLabel,
  onView,
  onDelete,
  onAction,
  onEdit,
  makeHead = false,
}: ActionContainerProps) {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleDelete = () => {
    setAnchorEl(null);
    if (onDelete) onDelete();
  };

  const handleEdit = () => {
    setAnchorEl(null);
    if (onEdit) onEdit();
  };

  const handleView = () => {
    setAnchorEl(null);
    if (onView) onView();
  };

  const handleClose = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(null);
    event.stopPropagation();
  };

  return (
    <>
      <div
        onClick={handleClick as any}
        aria-controls={open ? 'basic-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        className="pt-[5px]"
      >
        <span className="cursor-pointer text-[#8996A2] hover:text-black">
          <MoreHorizIcon />
        </span>
      </div>
      <Menu
        id="basic-menu"
        sx={{
          '& .MuiPaper-root': {
            minWidth: '250px',
            minHeight: '',
            backgroundColor: '#001018',
            boxShadow: 0,
            borderRadius: '18px',
            color: 'white',
            cursor: 'pointer',
          },
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
      >
        {onView && (
          <MenuItem
            className="justify-center text-[#0ABF89] text-center text-sm"
            onClick={(event) => {
              event.stopPropagation();
              handleView();
            }}
          >
            {viewLabel}
          </MenuItem>
        )}
        {onDelete && (
          <MenuItem
            className="text-sm justify-center hover:text-red-500"
            onClick={(event) => {
              event.stopPropagation();
              handleDelete();
            }}
          >
            {deleteLabel}
          </MenuItem>
        )}
        {makeHead && (
          <MenuItem
            className="justify-center text-[#0ABF89] text-center text-sm"
            onClick={onAction}
          >
            Make Head of Household
          </MenuItem>
        )}
        {onEdit && (
          <MenuItem
            className="justify-center text-[#0ABF89] text-center text-sm"
            onClick={(event) => {
              event.stopPropagation();
              handleEdit();
            }}
          >
            {editLabel}
          </MenuItem>
        )}
      </Menu>
    </>
  );
}
export default ActionContainer;
