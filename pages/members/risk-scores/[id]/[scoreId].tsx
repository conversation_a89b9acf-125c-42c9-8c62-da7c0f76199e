import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import MemberLayout from 'layouts/member-details-layout';
import API from 'api/src/lib/api';
import SafeDate from 'components/date-time/SafeDate';
import { capitalizeWords } from 'utils/helper';
import type {
  RiskScoreDetail,
  RiskScoreReferenceType,
  RiskScoreHistory,
} from 'types/risk-scores-types';

interface RiskScoreDetailProps {
  member: any;
  riskScore?: RiskScoreDetail | null;
}

const levelColors: Record<string, string> = {
  Low: '#28a745',
  Medium: '#ffc107',
  High: '#dc3545',
};

const referenceTypeLabels: Partial<Record<RiskScoreReferenceType, string>> = {
  assessment: 'Assessment',
  attachment: 'Attachment',
  careplan: 'Care Plan',
  note: 'Note',
  hospitalization: 'Hospitalization',
};

function RiskScoreDetailPage({ member, riskScore }: RiskScoreDetailProps) {
  const hasReferences = Boolean(riskScore?.references?.length);

  return (
    <MemberLayout
      memberId={member?.id}
      active="risk-scores"
      fullName={`${member?.firstName ?? ''} ${member?.lastName ?? ''}`.trim()}
      updateLabel="Risk Scores"
      updateUrl={`/members/risk-scores/${member?.id}`}
      add
      addLabel={riskScore ? `Score ${riskScore.score}` : 'Score Details'}
    >
      <div className="px-10 pb-10">
        {!riskScore ? (
          <div className="rounded-lg border border-dashed border-gray-300 bg-white p-10 text-center">
            <p className="text-lg font-medium text-gray-700 mb-2">Risk score not found</p>
            <p className="text-sm text-gray-500 m-0">
              This score may have been removed. Return to the list of risk scores to view available entries.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex flex-wrap items-end justify-between gap-6">
                <div>
                  <p className="text-sm uppercase tracking-wide text-gray-500 mb-1">Risk Score</p>
                  <div className="flex items-center gap-4">
                    <span className="text-4xl font-semibold text-gray-900">{riskScore.score}</span>
                    <span
                      className="text-sm font-medium px-3 py-1 rounded-full"
                      style={{
                        backgroundColor: `${levelColors[riskScore.level] ?? '#E5E7EB'}1A`,
                        color: levelColors[riskScore.level] ?? '#374151',
                      }}
                    >
                      {capitalizeWords(riskScore.level, true)}
                    </span>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-y-3 gap-x-8 min-w-[280px]">
                  <div>
                    <p className="text-xs uppercase text-gray-500 mb-1">Source</p>
                    <p className="text-sm font-medium text-gray-900">
                      {capitalizeWords(riskScore.source, true)}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs uppercase text-gray-500 mb-1">Created</p>
                    <p className="text-sm font-medium text-gray-900">
                      <SafeDate date={riskScore.createdAt} />
                    </p>
                  </div>
                  <div>
                    <p className="text-xs uppercase text-gray-500 mb-1">Last Updated</p>
                    <p className="text-sm font-medium text-gray-900">
                      <SafeDate date={riskScore.updatedAt} />
                    </p>
                  </div>
                  <div>
                    <p className="text-xs uppercase text-gray-500 mb-1">Created By</p>
                    <p className="text-sm font-medium text-gray-900">
                      {riskScore.createdByUser
                        ? `${riskScore.createdByUser.firstName ?? ''} ${riskScore.createdByUser.lastName ?? ''}`.trim() || riskScore.createdByUser.email
                        : '—'}
                    </p>
                    {riskScore.createdByUser?.email && (
                      <p className="text-xs text-gray-500">
                        {riskScore.createdByUser.email}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {riskScore.notes && (
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-3">Notes</h2>
                <p className="text-sm text-gray-700 leading-6 whitespace-pre-line">{riskScore.notes}</p>
              </div>
            )}

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-start justify-between gap-4 mb-6">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 mb-1">Supporting References</h2>
                  <p className="text-sm text-gray-500 m-0">
                    {hasReferences ? 'These items were linked when the score was created.' : 'No supporting references were provided for this risk score.'}
                  </p>
                </div>
                {hasReferences && (
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-1 rounded-full">
                    {riskScore.references?.length} reference{riskScore.references && riskScore.references.length !== 1 ? 's' : ''}
                  </span>
                )}
              </div>

              {hasReferences ? (
                <div className="space-y-3">
                  {riskScore.references?.map((reference) => (
                    <div
                      key={`${reference.ref_id}-${reference.type}`}
                      className="border border-gray-200 rounded-lg p-4 bg-white shadow-sm"
                    >
                      <div className="flex flex-wrap items-start justify-between gap-4">
                        <div className="flex items-center gap-3">
                          <span className="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-full font-medium">
                            {referenceTypeLabels[reference.type] ?? capitalizeWords(reference.type, true)}
                          </span>
                          <span className="font-medium text-gray-900">{reference.title}</span>
                        </div>
                        <span className="text-xs text-gray-400">ID: {reference.ref_id}</span>
                      </div>
                      {reference.reason && (
                        <p className="text-sm text-gray-600 mt-3 mb-0 leading-6">{reference.reason}</p>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="rounded-lg border border-dashed border-gray-200 bg-gray-50 p-8 text-center text-sm text-gray-500">
                  Add references when editing future scores so reviewers understand the key drivers.
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req, query } = context;
    let token;
    let org;

    const memberId = query.id as string;
    const scoreId = query.scoreId as string;

    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token;
      org = cookies.orgID;
    }

    const memberPromise = API.MEMBERS.fetchMember(memberId, token, org);

    let riskScore: RiskScoreDetail | null = null;
    try {
      riskScore = await API.MEMBERS.fetchMemberRiskScore(memberId, scoreId, token);
    } catch (error) {
      // Gracefully fall back to the history list until the detail endpoint is available
      try {
        const history = await API.MEMBERS.fetchMemberRiskScores(memberId, token);
        if (Array.isArray(history)) {
          const match = (history as RiskScoreHistory[]).find((item) => item.id === scoreId);
          if (match) {
            riskScore = {
              id: match.id,
              score: match.score,
              level: match.level,
              source: match.source,
              notes: match.notes ?? '',
              createdAt: match.createdAt,
              updatedAt: match.createdAt,
              references: [],
            } as RiskScoreDetail;
          }
        }
      } catch (historyError) {
        console.error('Unable to load fallback risk score history:', historyError);
      }
    }

    const member = await memberPromise;

    return {
      props: {
        member: member || null,
        riskScore: riskScore || null,
      },
    };
  } catch (error) {
    return {
      props: {
        member: null,
        riskScore: null,
      },
    };
  }
}

export default RiskScoreDetailPage;
