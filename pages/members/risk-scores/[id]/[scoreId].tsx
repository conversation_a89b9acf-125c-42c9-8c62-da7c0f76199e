import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import MemberLayout from 'layouts/member-details-layout';
import API from 'api/src/lib/api';
import SafeDate from 'components/date-time/SafeDate';
import { capitalizeWords } from 'utils/helper';
import type {
  RiskScoreDetail,
  RiskScoreReferenceType,
  RiskScoreHistory,
} from 'types/risk-scores-types';

interface RiskScoreDetailProps {
  member: any;
  riskScore?: RiskScoreDetail | null;
}

const levelColors: Record<string, string> = {
  Low: '#28a745',
  Medium: '#ffc107',
  High: '#dc3545',
};

const referenceTypeLabels: Partial<Record<RiskScoreReferenceType, string>> = {
  assessment: 'Assessment',
  attachment: 'Attachment',
  careplan: 'Care Plan',
  note: 'Note',
  hospitalization: 'Hospitalization',
};

function RiskScoreDetailPage({ member, riskScore }: RiskScoreDetailProps) {
  const hasReferences = Boolean(riskScore?.references?.length);

  return (
    <MemberLayout
      memberId={member?.id}
      active="risk-scores"
      fullName={`${member?.firstName ?? ''} ${member?.lastName ?? ''}`.trim()}
      updateLabel="Risk Scores"
      updateUrl={`/members/risk-scores/${member?.id}`}
      add
      addLabel={riskScore ? `Score ${riskScore.score}` : 'Score Details'}
    >
      <div className="max-w-7xl mx-auto px-6 py-8">
        {!riskScore ? (
          <div className="rounded-xl border border-dashed border-gray-300 bg-white p-12 text-center shadow-sm">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Risk Score Not Found</h3>
            <p className="text-gray-600 max-w-md mx-auto">
              This score may have been removed or you don't have permission to view it. Return to the list of risk scores to view available entries.
            </p>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Hero Section - Risk Score Display */}
            <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-2xl p-8 shadow-sm">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-24 h-24 rounded-full mb-4"
                     style={{
                       backgroundColor: `${levelColors[riskScore.level] ?? '#E5E7EB'}15`,
                       border: `2px solid ${levelColors[riskScore.level] ?? '#E5E7EB'}30`
                     }}>
                  <span className="text-4xl font-bold" style={{ color: levelColors[riskScore.level] ?? '#374151' }}>
                    {riskScore.score}
                  </span>
                </div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">Risk Score Assessment</h1>
                <div className="inline-flex items-center gap-2">
                  <span
                    className="text-lg font-semibold px-4 py-2 rounded-full"
                    style={{
                      backgroundColor: `${levelColors[riskScore.level] ?? '#E5E7EB'}20`,
                      color: levelColors[riskScore.level] ?? '#374151',
                    }}
                  >
                    {capitalizeWords(riskScore.level, true)} Risk
                  </span>
                </div>
              </div>

              {/* Metadata Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Source</h3>
                  </div>
                  <p className="text-lg font-semibold text-gray-900">
                    {capitalizeWords(riskScore.source, true)}
                  </p>
                </div>

                <div className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Created</h3>
                  </div>
                  <p className="text-lg font-semibold text-gray-900">
                    <SafeDate date={riskScore.createdAt} />
                  </p>
                </div>

                <div className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    </div>
                    <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Last Updated</h3>
                  </div>
                  <p className="text-lg font-semibold text-gray-900">
                    <SafeDate date={riskScore.updatedAt} />
                  </p>
                </div>

                <div className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Created By</h3>
                  </div>
                  <p className="text-lg font-semibold text-gray-900">
                    {riskScore.createdByUser
                      ? `${riskScore.createdByUser.firstName ?? ''} ${riskScore.createdByUser.lastName ?? ''}`.trim() || riskScore.createdByUser.email
                      : '—'}
                  </p>
                  {riskScore.createdByUser?.email && (
                    <p className="text-sm text-gray-500 mt-1">
                      {riskScore.createdByUser.email}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Notes Section */}
            {riskScore.notes && (
              <div className="bg-white border border-gray-200 rounded-2xl p-8 shadow-sm">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-yellow-100 rounded-xl flex items-center justify-center">
                    <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">Assessment Notes</h2>
                </div>
                <div className="bg-gray-50 rounded-xl p-6 border border-gray-100">
                  <p className="text-gray-700 leading-7 whitespace-pre-line text-base">{riskScore.notes}</p>
                </div>
              </div>
            )}

            {/* Supporting References Section */}
            <div className="bg-white border border-gray-200 rounded-2xl p-8 shadow-sm">
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-indigo-100 rounded-xl flex items-center justify-center">
                    <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">Supporting References</h2>
                    <p className="text-gray-600 mt-1">
                      {hasReferences ? 'Items linked when this score was created' : 'No supporting references were provided for this risk score'}
                    </p>
                  </div>
                </div>
                {hasReferences && (
                  <div className="flex items-center gap-2">
                    <span className="bg-indigo-100 text-indigo-800 text-sm font-semibold px-4 py-2 rounded-full">
                      {riskScore.references?.length} reference{riskScore.references && riskScore.references.length !== 1 ? 's' : ''}
                    </span>
                  </div>
                )}
              </div>

              {hasReferences ? (
                <div className="grid gap-4">
                  {riskScore.references?.map((reference, index) => (
                    <div
                      key={`${reference.ref_id}-${reference.type}`}
                      className="bg-gradient-to-r from-gray-50 to-white border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow duration-200"
                    >
                      <div className="flex items-start justify-between gap-4 mb-4">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-white border-2 border-gray-200 rounded-xl flex items-center justify-center font-bold text-gray-600">
                            {index + 1}
                          </div>
                          <div>
                            <div className="flex items-center gap-3 mb-2">
                              <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium">
                                {referenceTypeLabels[reference.type] ?? capitalizeWords(reference.type, true)}
                              </span>
                              <h3 className="font-semibold text-gray-900 text-lg">{reference.title}</h3>
                            </div>
                            {reference.reason && (
                              <p className="text-gray-600 leading-6 mt-2">{reference.reason}</p>
                            )}
                          </div>
                        </div>
                        <span className="text-xs text-gray-400 bg-gray-100 px-3 py-1 rounded-full font-mono">
                          ID: {reference.ref_id}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No References Available</h3>
                  <p className="text-gray-500 max-w-md mx-auto">
                    Add references when editing future scores so reviewers can understand the key drivers behind the assessment.
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req, query } = context;
    let token;
    let org;

    const memberId = query.id as string;
    const scoreId = query.scoreId as string;

    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token;
      org = cookies.orgID;
    }

    const memberPromise = API.MEMBERS.fetchMember(memberId, token, org);

    let riskScore: RiskScoreDetail | null = null;
    try {
      const response = await API.MEMBERS.fetchMemberRiskScore(memberId, scoreId, token);
      riskScore = response.data as RiskScoreDetail;
    } catch (error) {
      // Gracefully fall back to the history list until the detail endpoint is available
      try {
        const history = await API.MEMBERS.fetchMemberRiskScores(memberId, token);
        if (Array.isArray(history)) {
          const match = (history as RiskScoreHistory[]).find((item) => item.id === scoreId);
          if (match) {
            riskScore = {
              id: match.id,
              score: match.score,
              level: match.level,
              source: match.source,
              notes: match.notes ?? '',
              createdAt: match.createdAt,
              updatedAt: match.createdAt,
              references: [],
            } as RiskScoreDetail;
          }
        }
      } catch (historyError) {
        console.error('Unable to load fallback risk score history:', historyError);
      }
    }

    const member = await memberPromise;

    return {
      props: {
        member: member || null,
        riskScore: riskScore || null,
      },
    };
  } catch (error) {
    return {
      props: {
        member: null,
        riskScore: null,
      },
    };
  }
}

export default RiskScoreDetailPage;
