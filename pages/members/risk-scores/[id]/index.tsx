import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import type { AxiosError } from 'axios';
import API from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import { Button } from '@mui/material';
import React, { useState } from 'react';
import CustomTable from 'components/table-components/table';
import AddRiskScoreModal from 'components/members/risk-scores/AddRiskScoreModal';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { RiskScoreCreate, RiskScoreHistory } from 'types/risk-scores-types';

interface RiskScoresProps {
  member: any;
  riskScores: RiskScoreHistory[];
}

function RiskScores({ member, riskScores }: RiskScoresProps) {
  const [openModal, setOpenModal] = useState(false);
  const { refresh } = useRefreshSSR();

  const handleAddRiskScore = async (data: RiskScoreCreate) => {
    try {
      console.log('🚀 Sending risk score data to API:', data);
      console.log('📎 References in API call:', data.references);

      const response = await API.MEMBERS.createMemberRiskScore(member.id, data);

      console.log('✅ API Response:', response);
      refresh();
    } catch (error) {
      const err = error as AxiosError | Error;
      console.error('❌ Error adding risk score:', err);
      if ('response' in err && err.response) {
        console.error('📋 Error details:', err.response.data);
      } else {
        console.error('📋 Error details:', err instanceof Error ? err.message : err);
      }
    }
  };

  return (
    <MemberLayout
      memberId={member.id}
      active="risk-scores"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Risk Scores"
    >
      <div className="w-full pt-1 pb-1 justify-between pl-10 grid grid-flow-col auto-cols-max items-center">
        <p className="m-0 p-0 font-[600] text-lg">
          Risk Scores (
          {riskScores?.length || 0}
          )
        </p>
        <Button
          onClick={() => setOpenModal(true)}
          variant="contained"
          color="success"
          component="label"
          className="rounded-md m-0 p-0 h-[40px] float-right"
        >
          + Add Risk Score
        </Button>
      </div>

      <div className="w-full pl-10">
        {riskScores?.length > 0 ? (
          <CustomTable
            variant="member-risk-scores-table"
            source={riskScores}
            metadata={{ memberId: member.id }}
            style={{
              border: 'solid 1px lightgray',
              borderRadius: '10px',
              borderBottom: 'none',
              marginTop: '16px',
            }}
            headCellStyle={{
              padding: '4px 16px',
              fontSize: '13px',
              color: '#747A7A',
            }}
            tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
          />
        ) : (
          <p className="text-[15px] font-[400] m-0 mt-1">
            No risk scores have been recorded. Add the first one.
          </p>
        )}
      </div>

      <AddRiskScoreModal
        open={openModal}
        setOpen={setOpenModal}
        handleAdd={handleAddRiskScore}
        memberId={member.id}
      />
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let org;
    const memberId: string = context.query.id as string;

    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token;
      org = cookies.orgID;
    }

    const member = await API.MEMBERS.fetchMember(memberId, token, org);

    // Fetch risk scores - handle gracefully if API doesn't exist yet
    let riskScores: RiskScoreHistory[] = [];
    try {
      const response = await API.MEMBERS.fetchMemberRiskScores(memberId, token);
      console.log('Risk scores response:', response);
      riskScores = (response as unknown) as RiskScoreHistory[];
    } catch (riskScoreError) {
      console.log('Risk scores API not available yet:', riskScoreError);
      // Return mock data for testing until API is implemented
      riskScores = [
        {
          id: '1',
          score: 8,
          level: 'High',
          source: 'AssessmentEngine',
          createdAt: '2024-01-15T10:30:00Z',
        },
        {
          id: '2',
          score: 4,
          level: 'Medium',
          source: 'Manual',
          createdAt: '2024-01-10T14:20:00Z',
        },
        {
          id: '3',
          score: 2,
          level: 'Low',
          source: 'AssessmentEngine',
          createdAt: '2024-01-05T09:15:00Z',
        },
      ];
    }

    return {
      props: {
        member: member || [],
        riskScores: riskScores || [],
      },
    };
  } catch (error) {
    return {
      props: {
        member: [],
        riskScores: [],
      },
    };
  }
}

export default RiskScores;
